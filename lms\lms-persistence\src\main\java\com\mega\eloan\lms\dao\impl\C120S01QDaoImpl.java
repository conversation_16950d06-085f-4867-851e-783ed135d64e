/* 
 * C120S01QDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120S01QDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01Q;

/** 個金非房貸信用評等表 **/
@Repository
public class C120S01QDaoImpl extends LMSJpaDao<C120S01Q, String> implements
		C120S01QDao {

	@Override
	public C120S01Q findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01Q> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C120S01Q> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public C120S01Q findByUniqueKey(String mainId, String ownBrId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C120S01Q> findByIndex01(String mainId, String ownBrId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<C120S01Q> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C120S01Q> findByCustIdDupId(String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		List<C120S01Q> list = createQuery(C120S01Q.class, search)
				.getResultList();
		return list;
	}

	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120S01Q.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<C120S01Q> findByC160M02AJcicEtchIn1Month(String branchNo) {
		// UPGRADETODO: 修復 Hibernate Named Native Query 相容性問題
		// 這是 Native Query，不能指定 resultClass，讓 Hibernate 自動推斷結果類型
		Query query = entityManager.createNamedQuery("c120s01q.max_GrdCDate_jcic_etch");
		query.setParameter(1, branchNo);
		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<C120S01Q> findByC160M02AJcicEtchIn2Month(String branchNo) {
		// UPGRADETODO: 修復 Hibernate Named Native Query 相容性問題
		// 這是 Native Query，不能指定 resultClass，讓 Hibernate 自動推斷結果類型
		Query query = entityManager.createNamedQuery("c120s01q.max_GrdCDate_jcic_etch_2m");
		query.setParameter(1, branchNo);
		return query.getResultList();
	}
	
	@Override
	public int deleteByMainId(String mainId) {
		Query query = entityManager.createNamedQuery("c120s01q.deleteByMainId");
		query.setParameter(1, mainId);
		return query.executeUpdate();
	}
	
	@SuppressWarnings("unchecked")
	public List<C120S01Q> findBySrcMainId(String srcMainId) {
		// UPGRADETODO: 修復 Hibernate Named Native Query 相容性問題
		// 這是 Native Query，不能指定 resultClass，讓 Hibernate 自動推斷結果類型
		Query query = entityManager.createNamedQuery("c120s01q.findBySrcMainId");
		query.setParameter(1, srcMainId);
		return query.getResultList();
	}
}